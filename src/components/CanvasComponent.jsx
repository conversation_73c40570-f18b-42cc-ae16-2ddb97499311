import { useState, useRef, useCallback, useEffect } from 'react'

export const useCanvasHandler = (pdfPages, currentPageIndex) => {
  const [zoom, setZoom] = useState(1)
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 })
  const [isPanning, setIsPanning] = useState(false)
  const [panStart, setPanStart] = useState({ x: 0, y: 0 })
  const [showZoomIndicator, setShowZoomIndicator] = useState(false)
  
  const canvasRef = useRef(null)
  const baseImages = useRef({})

  // Convert screen coordinates to canvas coordinates
  const screenToCanvasCoordinates = useCallback((x, y) => {
    const canvas = canvasRef.current
    if (!canvas) {
      console.log('Canvas ref is null!')
      return { x: 0, y: 0 }
    }

    const rect = canvas.getBoundingClientRect()


    // Simple coordinate conversion for now
    const canvasX = ((x - rect.left) / rect.width) * canvas.width
    const canvasY = ((y - rect.top) / rect.height) * canvas.height

    // Ensure coordinates are within canvas bounds
    const boundedX = Math.max(0, Math.min(canvas.width, canvasX))
    const boundedY = Math.max(0, Math.min(canvas.height, canvasY))



    return {
      x: boundedX,
      y: boundedY
    }
  }, [zoom, canvasOffset])

  // Calculate optimal zoom for screen size
  const calculateOptimalZoom = useCallback(() => {
    if (pdfPages.length === 0) return 1

    const currentPage = pdfPages[currentPageIndex]
    if (!currentPage) return 1

    // Get available space (accounting for toolbar and potential sidebar)
    const availableWidth = window.innerWidth - 100 // Some padding
    const availableHeight = window.innerHeight - 200 // Account for toolbar

    const scaleX = availableWidth / currentPage.width
    const scaleY = availableHeight / currentPage.height

    // Use the smaller scale to ensure the PDF fits in the viewport
    const optimalZoom = Math.min(scaleX, scaleY, 1) // Don't zoom in beyond 100%



    return Math.max(0.2, optimalZoom) // Minimum zoom of 20%
  }, [pdfPages, currentPageIndex])

  // Auto-fit PDF to screen when switching PDFs or pages
  const autoFitToScreen = useCallback(() => {
    const optimalZoom = calculateOptimalZoom()
    setZoom(optimalZoom)
    setCanvasOffset({ x: 0, y: 0 }) // Reset pan when auto-fitting
  }, [calculateOptimalZoom])

  // Reset canvas position to center
  const resetCanvasPosition = useCallback(() => {
    setCanvasOffset({ x: 0, y: 0 })
  }, [])

  // Handle mouse wheel for zooming only (no scrolling)
  const handleWheel = useCallback((event) => {
    try {
      // Only handle zoom, prevent default scrolling
      event.preventDefault()
      event.stopPropagation()

      const zoomFactor = 0.1
      const delta = event.deltaY > 0 ? -zoomFactor : zoomFactor

      setZoom(currentZoom => {
        const newZoom = Math.max(0.1, Math.min(15, currentZoom + delta))
        return newZoom
      })

      // Show zoom indicator briefly
      setShowZoomIndicator(true)
      setTimeout(() => setShowZoomIndicator(false), 1000)
    } catch (error) {
      console.error('Error handling wheel event:', error)
    }
  }, []) // Remove zoom dependency to make handler stable

  // Handle panning
  const handlePanning = useCallback((event, drawingMode) => {
    if (drawingMode === 'hand') {
      setIsPanning(true)
      setPanStart({ x: event.clientX, y: event.clientY })
      return true
    }
    return false
  }, [])

  const handlePanMove = useCallback((event) => {
    if (isPanning) {
      const deltaX = event.clientX - panStart.x
      const deltaY = event.clientY - panStart.y

      setCanvasOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }))

      setPanStart({ x: event.clientX, y: event.clientY })
      return true
    }
    return false
  }, [isPanning, panStart])

  const handlePanEnd = useCallback(() => {
    setIsPanning(false)
  }, [])

  // Initialize base images when PDF pages change with 100% zoom
  useEffect(() => {
    if (pdfPages.length > 0) {
      // Set default zoom to 100% (1.0) instead of auto-fit
      setZoom(1.0)
      setCanvasOffset({ x: 0, y: 0 })

      // Initialize base images
      pdfPages.forEach((page, index) => {
        if (!baseImages.current[index]) {
          const img = new Image()
          img.onload = () => {
            baseImages.current[index] = img
          }
          img.src = page.imageData
        }
      })
    }
  }, [pdfPages])

  // Add wheel event listener with proper options to prevent passive event warning
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || pdfPages.length === 0) return

    const wheelHandler = (event) => {
      handleWheel(event)
    }

    // Ensure canvas is ready before adding event listener
    if (canvas.width > 0 && canvas.height > 0) {
      canvas.addEventListener('wheel', wheelHandler, { passive: false })
    } else {
      // If canvas isn't ready, wait for next frame
      const rafId = requestAnimationFrame(() => {
        if (canvas.width > 0 && canvas.height > 0) {
          canvas.addEventListener('wheel', wheelHandler, { passive: false })
        }
      })
      return () => {
        cancelAnimationFrame(rafId)
        canvas.removeEventListener('wheel', wheelHandler)
      }
    }

    return () => {
      canvas.removeEventListener('wheel', wheelHandler)
    }
  }, [handleWheel, pdfPages.length]) // Re-attach when PDF changes

  return {
    // State
    zoom,
    canvasOffset,
    isPanning,
    showZoomIndicator,
    canvasRef,
    baseImages,
    
    // Actions
    setZoom,
    setCanvasOffset,
    screenToCanvasCoordinates,
    autoFitToScreen,
    resetCanvasPosition,
    handlePanning,
    handlePanMove,
    handlePanEnd
  }
}
